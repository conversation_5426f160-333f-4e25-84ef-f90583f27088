name: Android Build

on:
  pull_request:
    branches:
      - '*'

env:
  FILE_PATH: ./android/app/build/outputs/apk/debug/app-debug.apk

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Use Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm install

      - name: Set up JDK 11
        uses: actions/setup-java@v2
        with:
          java-version: '11'
          distribution: 'adopt'

      - name: Check eslint
        run: npm run lint

      - name: Run tests
        run: npm run test

#      - name: Make APK
#        run: make build-apk-debug
