// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        kotlinVersion = "1.9.0"
        buildToolsVersion = "33.0.0"
        minSdkVersion = 21
        compileSdkVersion = 35
        targetSdkVersion = 35
        googlePlayServicesAuthVersion = "19.2.0" // <--- use this version or newer

        // We use NDK 23 which has both M1 support and is the side-by-side NDK version from AGP.
        ndkVersion = "23.1.7779620"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:7.4.2")
        classpath("com.bugsnag:bugsnag-android-gradle-plugin:7.+")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath('com.google.android.gms:play-services:9.6.1')
        //----FIREBASE----
        classpath 'com.google.gms:google-services:4.3.15'
        //----DETOX----
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion") // (3)
    }
}

allprojects {
    repositories {
        google()
        maven { // (4)
            url("$rootDir/../node_modules/detox/Detox-android")
        }
        maven { url 'https://www.jitpack.io' }
    }
}

