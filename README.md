# Brum

# Production build android

## Prepare application to release on android with <PERSON><PERSON><PERSON>
- [x] run `make generate-key`
- [x] run `make move-key`

### Open android/graddle.properties
- [x] add at the bottom of file: 
````
RELEASE_STORE_FILE=release.keystore
RELEASE_KEY_ALIAS=release
RELEASE_STORE_PASSWORD=USTAWIONE_HASLO
RELEASE_KEY_PASSWORD=USTAWIONE_HASLO
````

### Open android\app\build.gradle
- [x] add `signingConfigs` and `buildTypes` to `android` section
````
android {
....
  signingConfigs {
    release {
        storeFile file(RELEASE_STORE_FILE)
        storePassword RELEASE_STORE_PASSWORD
        keyAlias RELEASE_KEY_ALIAS
        keyPassword RELEASE_KEY_PASSWORD
    }
  }
  buildTypes {
    release {
      ....
      signingConfig signingConfigs.release
    }
  }
}
````
### Open android\app\src\main\AndroidManifest.xml
Add this line
```
<application
  ...
  android:usesCleartextTraffic="true"
  ...
>
   ...
</application>
```
- [x] Remove developer app from phone
- [x] run `rm -rf ./android/app/src/main/res/drawable-*`
- [x] run `rm -rf ./android/app/src/main/res/raw`
- [x] add `release.keystore` to `.gitignore` file
- [x] run `make build-apk-release`
- [x] run `make build-app-release` -- tego nie trzeba
- [x] run `make install-apk`

### Jeśli nie zadziała patrz:
```
https://stackoverflow.com/questions/57289466/react-native-android-app-not-working-in-release
https://medium.com/geekculture/react-native-generate-apk-debug-and-release-apk-4e9981a2ea51
```

## Workflows
`main.yml`: 
- [x] auto build and deploy to google drive

## Scripts
- `iconsBuild.js`: 
  - [x] generate icons for android and ios in `src/assets` folder

- `upload.sh`:
    - [x] upload apk to google drive. Script expect 3 arguments: `access_token`, `file_path`, `folder_id`

# Fonts
- [x] ***required*** package: `react-native-assets`
- [x] create folder `src/assets/fonts`
- [x] add these lines to `react-native.config.js`
````
module.exports = {
  project: {
    ios: {},
    android: {},
  },
  assets: ['./src/assets/'],
};
````
- [x] add `<Font-Name>.ttf` to `src/theme/index` eg.: 
````
primary: 'Inter-Regular',
````
- [x] run `npm start`


# Application structure

## Enums
- [x] `couponEnum.ts`: 
```typescript
export enum CouponValueType {
  OTHER = 0,
  PERCENTAGE_TO = 1,
  PERCENTAGE_FROM = 2,
  CURRENCY_TO = 3,
  CURRENCY_FROM = 4,
}
```
Equivalent field from api value_type. It determines how the card is displayed. If "other" is selected, only the discount value will be displayed

## API INTERFACES

For generate interfaces from api schema use
```bash
npm run api-schema
```

## Components build
```bash
npm run component-build NazwaKompoentu ./src/components
```


## Call Fech and Notification on app sleep

### Uwaga - poniższy przykład działa, ale wymaga wielu popraw - to tylko przykład wywołania

Wymagane biblioteki - dodaj do package.json;
```bash
"@notifee/react-native": "^7.8.0", 
"react-native-background-fetch": "^4.2.1",
```

Poniższy kod umieszczamy w index.js
```js
async function onDisplayNotification() {
  // Request permissions (required for iOS)
  await notifee.requestPermission();

  // Create a channel (required for Android)
  const channelId = await notifee.createChannel({
    id: 'default',
    name: 'Default Channel',
  });

  // Display a notification
  await notifee.displayNotification({
    title: 'BRUM BRUM BRUM!',
    body: 'Main body content of the notification',
    android: {
      channelId,
      smallIcon: 'ic_launcher',
      //smallIcon: 'name-of-a-small-icon', // optional, defaults to 'ic_launcher'.
      // pressAction is needed if you want the notification to open the app when pressed
      pressAction: {
        id: 'default',
      },
    },
  });
}

BackgroundFetch.configure(
        {
          minimumFetchInterval: 15, // Interwał w minutach
          stopOnTerminate: false, // Nie zatrzymuj po zakończeniu aplikacji
          startOnBoot: true, // Uruchamiaj po restarcie urządzenia
          enableHeadless: true, // Uruchamiaj w trybie tła
          //forceAlarmManager: true, // Użyj AlarmManager (opcjonalne)
        },
        async (taskId) => {
          console.log('BackgroundFetch');
          try {
            // Wywołaj funkcję pobierania danych z API i wyświetlania powiadomienia
            //const data = await fetchDataFromAPI();
            console.log('BackgroundFetch');
            await onDisplayNotification();
            BackgroundFetch.finish(taskId);
          } catch (error) {
            console.error(error);
            BackgroundFetch.finish(taskId);
          }
        },
        async (taskId) => {
          BackgroundFetch.finish(taskId);
        },
).then(() => {});

// Rozpocznij BackgroundFetch
BackgroundFetch.start().then(() => {
  console.log('BackgroundFetch started');
});
```

Dla androida musimy wykonać konfigurację:
Dodajemy to do android/build.gradle na sam dół pliku
```gradle
allprojects {
    repositories {
        mavenLocal()
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            // Android JSC is installed from npm
            url("$rootDir/../node_modules/jsc-android/dist")
        }
        maven {
            // react-native-background-fetch
            url("${project(':react-native-background-fetch').projectDir}/libs")
        }

    }
}
```

Dla IOS - aplikacja kompiluje się poprawnie ale wymaga sprawdzenia (nie testowane)
link do konfigów
```bash
https://github.com/transistorsoft/react-native-background-fetch/blob/HEAD/docs/INSTALL-AUTO-ANDROID.md
```

# Firebase

## Ios
**run firebase DebugView for ios devices:**
- Open project `ios` in Xcode
- Select `Products`
- Select `Scheme` -> `Edit Scheme`
- Select `Run`
- `Press +` and add `-FIRDebugEnabled`

## Android
**run firebase DebugView for android devices:**
- Run: `adb shell setprop debug.firebase.analytics.app com.brum`  


## Detox testing 

### Config

```bash
npm install detox --save-dev
npm install -g detox-cli
```

Zmień w .detoxrc.js nazwy projektu i symulatora IOS
```
Linia 40: type: 'iPhone SE (3rd generation)';
Linia 13-24: zmień nazwy YOUR_APP na nazwe projetu
```

Zainstaluj dodatkowe paczki dla IOS
```bash
brew tap wix/brew
brew install applesimutils
```

Zbuduj testy dla symulatora IOS
```bash
detox build --configuration ios.sim.release
lub
detox build --configuration ios.sim.debug
```

Uruchom testy dla symulatora IOS
```bash
detox test --configuration ios.sim.release
lub
detox test --configuration ios.sim.debug
```

### BRUM DEPLOY FOR IOS AND ANDROID

##Show fingerprint for Android google firebase auth

# For production
```
keytool -list -v -keystore ./android/app/homeprofit-key.keystore -alias homeprofit-key -storepass Yg2h88mU -keypass Yg2h88mU
```


## Environment settings

- Node Version: v20.18.1
- Java Version: v17.0.8-tem

Java setup:
```
sdk list java             # pokazuje dostępne wersje
sdk install java 17.0.8-tem   # instaluje JDK 17
sdk use java 17.0.8-tem       # używa JDK 17 dla bieżącego terminala
sdk default java 17.0.8-tem   # ustawia domyślną wersję
```