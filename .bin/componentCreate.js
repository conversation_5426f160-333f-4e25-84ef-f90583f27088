const fs = require('fs');
const path = require('path');

// Tworzenie funkcji asynchronicznych do tworzenia plików
async function createFile(fileName, data, folderPath) {
  console.log(folderPath);
  if (fs.existsSync(folderPath)) {
    const filePath = path.join(folderPath, fileName);
    await fs.promises.writeFile(filePath, data, 'utf8');
    console.log(`Utworzono plik: ${filePath}`);
  } else {
    //console.log(`Błąd ścieżki: ${folderPath}`);
    setTimeout(() => {
      console.log('Loading ...');
      // Tu<PERSON>j um<PERSON> kod, który ma zostać wykonany po odczekaniu 2 sekund
    }, 2000);
  }
}

async function createFolder(folderPath) {
  if (!fs.existsSync(folderPath)) {
    await fs.promises.mkdir(folderPath);
    console.log(`Utworzono folder: ${folderPath}`);
  } else {
    console.log(`Folder już istnieje: ${folderPath}`);
  }
}

// Właściwa logika skryptu
const folderPath = process.argv[3]; // Pobieranie ścieżki do folderu z argumentu wiersza poleceń
const componentName = process.argv[2];

if (!folderPath) {
  console.error('Nie podano ścieżki do folderu.');
  process.exit(1);
}

const indexString = `import * as Styled from './${componentName}.styled';

export const ${componentName} = () => {
  return (
    <Styled.Wrapper>
      <Styled.Text>${componentName}</Styled.Text>
    </Styled.Wrapper>
  );
};`;

const styledString = `import styled from '@emotion/native';

export const Wrapper = styled.View\`\`;

export const Text = styled.Text\`\`;
`;

// Tworzenie folderu
(async () => {
  await createFolder(`${folderPath}/${componentName}`);
  await createFolder(`${folderPath}/${componentName}/components`);

  await createFile(`${componentName}.tsx`, indexString, `${folderPath}/${componentName}/`);
  await createFile(`${componentName}.styled.ts`, styledString, `${folderPath}/${componentName}/`);
  await createFile('types.d.ts', '', `${folderPath}/${componentName}/`);
})();
