const fs = require('fs');

const outputFilePath = './src/theme/';
const outputFileName = 'assets.tsx';
const defaultPath = './src/assets/';
const fileIgnore = ['.DS_Store', 'fonts', '*.ttf'];
const iconsComponent = {};
const importPath = [];

function changeSign(value, znak) {
  let parts = value.split(znak);

  for (let i = 0; i < parts.length - 1; i++) {
    parts[i] = parts[i] + znak + parts[i + 1].charAt(0).toUpperCase() + parts[i + 1].substring(1);
    parts.splice(i + 1, 1);
  }

  value = parts.join('');
  return value;
}

function showFiles(path, dirKey) {
  fs.readdirSync(`${defaultPath}${path}`).forEach((file) => {
    if (fileIgnore.includes(file)) {
      return;
    }

    const fileType = fs.lstatSync(`${defaultPath}${path}/${file}`).isDirectory();

    if (fileType) {
      iconsComponent[`${file}`] = {};
      showFiles(`${path}/${file}`, file);
    } else {
      const ext = file.split('.')[1];
      let key = file.replace('.svg', '');
      key = key.replace('.png', '');
      key = key.replace('.gif', '');
      let keyComponent = key.replace(/^\w/, (c) => c.toUpperCase());
      keyComponent = changeSign(keyComponent, '_');
      keyComponent = keyComponent.replace('_', '');
      console.log(`\x1b[32m\u{2713} file \x1b[0m- \x1b[34m${key}\x1b[31m.${ext}`);
      if (ext !== 'svg') {
        if(ext === 'gif') {
          keyComponent = keyComponent + 'Gif';
        } else {
          keyComponent = keyComponent + 'Png';
        }
      }

      if (dirKey) {
        iconsComponent[`${dirKey}`][`${key}_${ext}`] = `${keyComponent}`;
      } else {
        iconsComponent[`${key}_${ext}`] = `${keyComponent}`;
      }

      let importPattern = `${keyComponent}`;

      if (ext !== 'svg') {
        importPath.push(`const ${importPattern} = require("~/assets${path}/${file}");`);
      } else {
        importPath.push(`import ${importPattern} from "~/assets${path}/${file}";`);
      }
    }
  });
}

function parseImports() {
  let data = '';
  for (let i = 0; i < importPath.length; i++) {
    data += importPath[i] + '\n';
  }
  data += '\n';
  return data;
}

function parseJson() {
  let variable = 'export const assets = ';
  let data = JSON.stringify(iconsComponent);
  data = data.replaceAll('"', '');
  data = data.replace('{', '{\n\t');
  data = data.replaceAll(',', ',\n\t');
  data = data.replace('}', '\n}');
  data = data.replaceAll(':', ': ');
  data = variable + data;
  return data;
}

function saveFile(data, imports) {
  fs.writeFileSync(`${outputFilePath}/${outputFileName}`, `${imports}${data}`);
}

new Promise((resolve) => {
  console.log('\x1b[32m\u{1F4BE} \x1b[0m- \x1b[34mIcon Assets Builder\x1b[0m');
  showFiles('');
  resolve();
})
  .then(() => {
    const imports = parseImports();
    const data = parseJson();
    saveFile(data, imports);
  })
  .finally(() => {
    console.log('\n\x1b[0mCreated by \u{1F600} \x1b[32mCodeB.me');
  });
