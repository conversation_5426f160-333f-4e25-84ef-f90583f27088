#!/bin/bash

ACCESS_TOKEN="$1"
FILE_PATH="$2"
FILE_NAME=$(basename "$FILE_PATH")
MIME_TYPE=$(file -b --mime-type "$FILE_PATH")
FOLDER_ID="$3"
CHUNK_SIZE=5242880 # 5MB
FILE_SIZE=$(wc -c <"$FILE_PATH")

# Step 1: Create an upload session
SESSION_URI=$(curl -X POST \
  --url "https://www.googleapis.com/upload/drive/v3/files?uploadType=resumable" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
        "name": "'"$FILE_NAME"'",
        "mimeType": "'"$MIME_TYPE"'",
        "parents": ["'"$FOLDER_ID"'"]
      }' \
  -D - | grep -i "^Location:" | awk '{print $2}' | tr -d '\r')

echo "File name: $FILE_NAME"
echo "File path: $FILE_PATH"
echo "Access token: $ACCESS_TOKEN"
echo "Folder ID: $FOLDER_ID"
echo "Session URI: $SESSION_URI"

# Step 2: Upload the file in chunks
START=0
END=$((CHUNK_SIZE-1))

while [[ $START -lt $FILE_SIZE ]]
do
  if [[ $END -gt $FILE_SIZE ]]; then
    END=$((FILE_SIZE-1))
  fi
  echo "Uploading $FILE_NAME range: $START-$END of $FILE_SIZE"
  dd if="$FILE_PATH" bs=1 skip="$START" count=$((END-START+1)) | \
    curl -v -X PUT \
      -H "Authorization: Bearer $ACCESS_TOKEN" \
      -H "Content-Type: $MIME_TYPE" \
      -H "Content-Range: bytes $START-$END/$FILE_SIZE" \
      --data-binary @- \
      "$SESSION_URI"
  START=$((START + CHUNK_SIZE))
  END=$((END + CHUNK_SIZE))
done
